"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Area, AreaChart, Pie, PieChart, ResponsiveContainer, Tooltip } from "recharts";
import { Landmark, Laptop, ArrowUpRight, ArrowDownRight, ShieldCheck, CreditCard, PiggyBank, Car } from "lucide-react";

const chartData = [
  { month: "Enero", desktop: ******** },
  { month: "Febrero", desktop: ******** },
  { month: "Marzo", desktop: ******** },
  { month: "Abril", desktop: ******** },
  { month: "Mayo", desktop: ******** },
  { month: "Junio", desktop: ******** },
];

const chartConfig = {
  desktop: {
    label: "Patrimonio",
    color: "hsl(var(--chart-1))",
  },
};

const donutChartData = [
    { browser: "Azul", visitors: 60, fill: "hsl(var(--chart-1))" },
    { browser: "Verde", visitors: 30, fill: "hsl(var(--chart-2))" },
    { browser: "Amarillo", visitors: 10, fill: "hsl(var(--chart-3))" },
];

const donutChartConfig = {
    visitors: {
        label: "Visitantes",
    },
    azul: {
        label: "Acciones",
        color: "hsl(var(--chart-1))",
    },
    verde: {
        label: "Bonos",
        color: "hsl(var(--chart-2))",
    },
    amarillo: {
        label: "Efectivo",
        color: "hsl(var(--chart-3))",
    },
};

const allTransactions = [
  { name: "Salario", amount: "+$4,500,000", type: "income" as const },
  { name: "Supermercado", amount: "-$128,700", type: "expense" as const },
  { name: "Restaurante", amount: "-$45,300", type: "expense" as const },
  { name: "Transferencia", amount: "+$2,000,000", type: "income" as const },
  { name: "Suscripción", amount: "-$15,000", type: "expense" as const },
  { name: "Venta online", amount: "+$75,500", type: "income" as const },
  { name: "Cafetería", amount: "-$12,500", type: "expense" as const },
  { name: "Transporte", amount: "-$22,000", type: "expense" as const },
  { name: "Cine", amount: "-$25,000", type: "expense" as const },
  { name: "Devolución", amount: "+$30,000", type: "income" as const },
  { name: "Arriendo", amount: "-$450,000", type: "expense" as const },
  { name: "Cuentas (Luz, Agua)", amount: "-$55,000", type: "expense" as const },
  { name: "Plan Celular", amount: "-$18,000", type: "expense" as const },
  { name: "Gimnasio", amount: "-$25,000", type: "expense" as const },
  { name: "Bencina", amount: "-$40,000", type: "expense" as const },
  { name: "Farmacia", amount: "-$19,500", type: "expense" as const },
  { name: "Ropa", amount: "-$60,000", type: "expense" as const },
  { name: "Regalo", amount: "-$35,000", type: "expense" as const },
  { name: "Venta Garage", amount: "+$50,000", type: "income" as const },
  { name: "Freelance", amount: "+$150,000", type: "income" as const },
  { name: "Bono", amount: "+$200,000", type: "income" as const },
  { name: "Comida a domicilio", amount: "-$18,900", type: "expense" as const },
  { name: "Concierto", amount: "-$75,000", type: "expense" as const },
  { name: "Intereses Ganados", amount: "+$5,200", type: "income" as const },
  { name: "Reparación auto", amount: "-$120,000", type: "expense" as const },
];

const goals = [
    { icon: Landmark, name: "Viaje a Japón", current: 3750000, total: 5000000 },
    { icon: Laptop, name: "Nuevo Portátil", current: 1200000, total: 1500000 },
    { icon: ShieldCheck, name: "Fondo de Emergencia", current: 120000, total: 2000000 },
    { icon: Car, name: "Renovar el Auto", current: 4000000, total: ******** },
    { icon: PiggyBank, name: "Ahorrar para pie de Depto.", current: 1500000, total: ******** },
    { icon: CreditCard, name: "Pagar Tarjeta de Crédito", current: 500000, total: 500000 },
];

const parseAmount = (amount: string) => {
    return parseFloat(amount.replace(/[^0-9-]/g, ""));
}

function GoalProgressBar({ goal, index }: { goal: typeof goals[0], index: number }) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const calculatedProgress = (goal.current / goal.total) * 100;
    const timer = setTimeout(() => setProgress(calculatedProgress), 100 + index * 150);
    return () => clearTimeout(timer);
  }, [goal, index]);

  const isCompleted = progress >= 100;
  const isAlmostThere = progress >= 70 && progress < 100;
  const isOverHalf = progress > 50 && progress < 70;
  const isUnderHalf = progress <= 50 && progress > 25;
  const isVeryLow = progress <= 25;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP', minimumFractionDigits: 0 }).format(value);
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-2">
        <div className="p-2 bg-secondary rounded-md">
          <goal.icon className={cn("h-6 w-6",
            isCompleted && "text-green-500",
            isAlmostThere && "text-lime-400",
            isOverHalf && "text-yellow-400",
            isUnderHalf && "text-orange-400",
            isVeryLow && "text-red-500"
          )} />
        </div>
        <div>
          <p className="font-semibold">{goal.name}</p>
          <p className="text-sm text-muted-foreground">
            {formatCurrency(goal.current)} de {formatCurrency(goal.total)}
          </p>
        </div>
        <p className={cn("ml-auto font-bold",
          isCompleted ? "text-green-500" :
          isAlmostThere ? "text-lime-400" :
          isOverHalf ? "text-yellow-400" :
          isUnderHalf ? "text-orange-400" :
          isVeryLow ? "text-red-500" :
          "text-primary"
        )}>{Math.round(progress)}%</p>
      </div>
      <Progress value={progress} className={cn("h-2",
        isCompleted ? "[&>div]:bg-green-500" :
        isAlmostThere ? "[&>div]:bg-lime-400" :
        isOverHalf ? "[&>div]:bg-yellow-400" :
        isUnderHalf ? "[&>div]:bg-orange-400" :
        isVeryLow ? "[&>div]:bg-red-500" :
        "[&>div]:bg-primary"
      )} />
    </div>
  );
}


export function InteractiveDashboard() {
  const [isFlipped, setIsFlipped] = useState(false);
  const [transactions, setTransactions] = useState(allTransactions.slice(0, 4));

  const [totalBalance, setTotalBalance] = useState(********);
  const [monthlyIncome, setMonthlyIncome] = useState(9167600);
  const [monthlyExpenses, setMonthlyExpenses] = useState(3668480);


  useEffect(() => {
    const interval = setInterval(() => {
        let newTransaction;
        do {
          newTransaction = allTransactions[Math.floor(Math.random() * allTransactions.length)];
        } while (transactions.some(t => t.name === newTransaction.name));

        const amount = parseAmount(newTransaction.amount);

        setTransactions(prev => {
            const newTransactions = [newTransaction, ...prev.slice(0, 3)];
            return newTransactions;
        });
        
        if (newTransaction.type === 'income') {
            setTotalBalance(prev => prev + amount);
            setMonthlyIncome(prev => prev + amount);
        } else {
            setTotalBalance(prev => prev + amount); // amount is negative
            setMonthlyExpenses(prev => prev - amount);
        }

    }, 4000);
    return () => clearInterval(interval);
  }, [transactions]);
  
  useEffect(() => {
    const heroElement = document.getElementById('hero-section');
    if (!heroElement) return;

    const handleScroll = () => {
        const { top, height } = heroElement.getBoundingClientRect();
        const flipPoint = -height / 8;
        
        if (top < flipPoint && !isFlipped) {
            setIsFlipped(true);
        } else if (top >= flipPoint && isFlipped) {
            setIsFlipped(false);
        }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
        window.removeEventListener('scroll', handleScroll);
    };
  }, [isFlipped]);

  const formatCurrency = (value: number) => {
      return new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP', minimumFractionDigits: 0 }).format(value);
  }

  const savingRate = monthlyIncome > 0 ? Math.round(((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100) : 0;


  return (
    <div className={cn("relative w-full max-w-6xl h-[480px] transition-transform duration-1000 ease-in-out [transform-style:preserve-3d]", isFlipped ? "[transform:rotateY(180deg)]" : "")}>
        {/* Front Face */}
        <div className="absolute w-full h-full [backface-visibility:hidden]">
            <Card className="w-full h-full bg-card/60 backdrop-blur-sm border-white/5 shadow-2xl shadow-primary/5">
                <CardContent className="p-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <Card className="bg-card/70 border-none shadow-none">
                            <CardHeader className="p-3">
                                <CardDescription>Saldo Total</CardDescription>
                                <CardTitle>{formatCurrency(totalBalance)}</CardTitle>
                            </CardHeader>
                        </Card>
                        <Card className="bg-card/70 border-none shadow-none">
                            <CardHeader className="p-3">
                                <CardDescription>Ingresos (Mes)</CardDescription>
                                <CardTitle className="text-green-400">{formatCurrency(monthlyIncome)}</CardTitle>
                            </CardHeader>
                        </Card>
                         <Card className="bg-card/70 border-none shadow-none">
                            <CardHeader className="p-3">
                                <CardDescription>Gastos (Mes)</CardDescription>
                                <CardTitle className="text-orange-400">{formatCurrency(monthlyExpenses)}</CardTitle>
                            </CardHeader>
                        </Card>
                         <Card className="bg-card/70 border-none shadow-none">
                            <CardHeader className="p-3">
                                <CardDescription>Ahorro/Inversión</CardDescription>
                                <CardTitle>{savingRate}%</CardTitle>
                            </CardHeader>
                        </Card>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[350px]">
                        <Card className="lg:col-span-2 bg-card/70 border-none shadow-none">
                             <CardHeader className="p-3">
                                <CardTitle className="text-base">Patrimonio Neto</CardTitle>
                            </CardHeader>
                            <CardContent className="p-3 pt-0 h-[280px]">
                                <ChartContainer config={chartConfig}>
                                    <AreaChart accessibilityLayer data={chartData} margin={{ left: -20, right: 12, top: 12 }}>
                                        <defs>
                                            <linearGradient id="fillDesktop" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="5%" stopColor="var(--color-desktop)" stopOpacity={0.8} />
                                                <stop offset="95%" stopColor="var(--color-desktop)" stopOpacity={0.1} />
                                            </linearGradient>
                                        </defs>
                                        <Tooltip cursor={false} content={<ChartTooltipContent indicator="line" hideLabel hideIndicator />} />
                                        <Area dataKey="desktop" type="natural" fill="url(#fillDesktop)" stroke="var(--color-desktop)" stackId="a" strokeWidth={3} />
                                    </AreaChart>
                                </ChartContainer>
                            </CardContent>
                        </Card>
                        <div className="flex flex-col gap-4">
                            <Card className="flex-1 bg-card/70 border-none shadow-none">
                                <CardHeader className="p-3 text-center">
                                    <CardTitle className="text-base">Desglose de Cartera</CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 pt-0 h-[140px]">
                                    <ChartContainer config={donutChartConfig}>
                                        <ResponsiveContainer width="100%" height="100%">
                                            <PieChart>
                                                <Tooltip content={<ChartTooltipContent hideLabel />} />
                                                <Pie data={donutChartData} dataKey="visitors" nameKey="browser" innerRadius={35} outerRadius={50} strokeWidth={2} />
                                            </PieChart>
                                        </ResponsiveContainer>
                                    </ChartContainer>
                                </CardContent>
                            </Card>
                             <Card className="flex-1 bg-card/70 border-none shadow-none">
                                <CardHeader className="p-3">
                                    <CardTitle className="text-base">Transacciones Recientes</CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 pt-0">
                                    <ul className="space-y-3 mt-2 overflow-hidden">
                                        {transactions.map((t, i) => (
                                            <li key={`${t.name}-${i}`} className="flex justify-between items-center text-sm px-2 animate-fade-in-down">
                                                <div className="flex items-center gap-2">
                                                    {t.type === 'income' ? <ArrowUpRight className="h-4 w-4 text-green-400" /> : <ArrowDownRight className="h-4 w-4 text-orange-400" />}
                                                    <span>{t.name}</span>
                                                </div>
                                                <span className={cn('font-mono', t.type === 'income' ? 'text-green-400' : 'text-muted-foreground')}>{t.amount}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        {/* Back Face */}
        <div className="absolute w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]">
            <Card className="w-full h-full bg-card/60 backdrop-blur-sm border-white/10 shadow-2xl shadow-primary/10">
                <CardHeader>
                    <CardTitle>Metas y Objetivos</CardTitle>
                    <CardDescription className="text-base text-foreground/90">Visualiza y sigue tu progreso hacia tus metas financieras.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    {goals.map((goal, i) => (
                        <GoalProgressBar key={i} goal={goal} index={i} />
                    ))}
                </CardContent>
            </Card>
        </div>
    </div>
  );
}
