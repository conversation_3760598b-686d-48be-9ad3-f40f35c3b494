"use client";

import { useState, useEffect, useRef } from 'react';
import { Share2, Facebook, Linkedin } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

const InstagramIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        <rect width="20" height="20" x="2" y="2" rx="5" ry="5"/>
        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
        <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/>
    </svg>
);

const XIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" {...props}>
        <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 7.185L20.29.357h-1.39zm-1.16 19.57h1.837L7.056 2.522h-1.91l12.632 18.2z"/>
    </svg>
);

const RedditIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" {...props}>
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.14 12.2c-.28.28-.66.44-1.06.44h-6.16c-.4 0-.78-.16-1.06-.44-.28-.28-.44-.66-.44-1.06V12.4c0-1.1.9-2 2-2h4.5c1.1 0 2 .9 2 2v.74c0 .4-.16.78-.44 1.06zM10 8.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm4 0c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"/>
    </svg>
);


const socialLinks = [
    { icon: Facebook, href: "https://www.facebook.com/AsistentePersonal360/", name: "Facebook" },
    { icon: InstagramIcon, href: "https://www.instagram.com/asistenteintegral360/", name: "Instagram" },
    { icon: XIcon, href: "https://x.com/asistente360", name: "X" },
    { icon: RedditIcon, href: "https://www.reddit.com/user/MostEstablishment131/", name: "Reddit" },
    { icon: Linkedin, href: "https://www.linkedin.com/in/asistenteintegral360/", name: "LinkedIn" },
];

export function SocialShare() {
    const [isActive, setIsActive] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    const handleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsActive(prev => !prev);
    };

    const handleLinkClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsActive(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    
    return (
        <div id="neuronal-menu" ref={menuRef} className={cn('neuronal-menu', { 'is-active': isActive })} onClick={(e) => e.stopPropagation()}>
            <button className="share-button" onClick={handleClick} aria-expanded={isActive} aria-label="Compartir en redes sociales">
                <Share2 className="share-icon"/>
            </button>
            <div className="trace"></div>
            {socialLinks.map((social, index) => (
                <div key={social.name} className={cn('social-icon', `social-icon-${index + 1}`)}>
                     <Link href={social.href} target="_blank" rel="noopener noreferrer" onClick={handleLinkClick} aria-label={`Compartir en ${social.name}`}>
                         <social.icon className="social-svg"/>
                    </Link>
                </div>
            ))}
        </div>
    );
}
