@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 204 100% 6%;
    --foreground: 0 0% 98%;
    --card: 204 50% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 204 50% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 340 95% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 204 30% 25%;
    --muted-foreground: 204 5% 65%;
    --accent: 45 100% 50%;
    --accent-foreground: 204 100% 6%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 204 20% 20%;
    --input: 204 20% 20%;
    --ring: 340 95% 60%;
    --chart-1: 180 80% 45%;
    --chart-2: 210 80% 55%;
    --chart-3: 45 100% 50%;
    --chart-4: 160 80% 40%;
    --chart-5: 0 80% 60%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 204 100% 6%;
    --foreground: 0 0% 98%;
    --card: 204 50% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 204 50% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 340 95% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 204 20% 10%;
    --secondary-foreground: 0 0% 98%;
    --muted: 204 30% 25%;
    --muted-foreground: 204 5% 65%;
    --accent: 45 100% 50%;
    --accent-foreground: 204 100% 6%;
    --destructive: 0 85% 65%;
    --destructive-foreground: 0 0% 98%;
    --border: 204 20% 20%;
    --input: 204 20% 20%;
    --ring: 340 95% 60%;
    --chart-1: 180 80% 45%;
    --chart-2: 210 80% 55%;
    --chart-3: 45 100% 50%;
    --chart-4: 160 80% 40%;
    --chart-5: 0 80% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
