
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { InteractiveDashboard } from "./interactive-dashboard";
import { useMounted } from "@/hooks/use-mounted";
import { cn } from "@/lib/utils";
import { SocialShare } from "./social-share";

export function HeroSection() {
  const mounted = useMounted();

  const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    const href = e.currentTarget.href;
    const targetId = href.replace(/.*#/, "");
    const elem = document.getElementById(targetId);
    elem?.scrollIntoView({ behavior: "smooth", block: "center" });
  };

  return (
    <section id="hero-section" className="relative w-full overflow-hidden">
      {/* --- START: VISUAL DEBUGGING FLARES --- */}
      <div className="absolute top-0 left-0 w-full h-full -z-10">
        <div className="absolute top-0 left-0 w-[500px] h-[500px] bg-destructive/20 rounded-full blur-[150px] animate-pulse" style={{ animationDelay: '0s' }}></div>
        <div className="absolute bottom-0 right-0 w-[400px] h-[400px] bg-destructive/15 rounded-full blur-[120px] animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-destructive/10 rounded-full blur-[100px] animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>
      {/* --- END: VISUAL DEBUGGING FLARES --- */}
      
      <div className="absolute top-32 right-4 sm:right-8 md:right-16 z-20">
        <SocialShare />
      </div>
      <div className="absolute inset-0 -z-20 bg-gradient-to-br from-background via-background/90 to-background"></div>
      
      <div className="container mx-auto relative pt-16 pb-24 sm:pt-24 sm:pb-32 lg:pt-32 lg:pb-40">
        <div className="grid grid-cols-1 gap-12 items-center">
          <div className="flex flex-col items-center text-center">
            <h1 className={cn("text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl transition-all duration-1000 ease-out", mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8")}>
              <span className="relative inline-block">
                <span className="animate-shine-infinite bg-[linear-gradient(110deg,hsl(var(--foreground)),45%,hsl(var(--primary)),55%,hsl(var(--foreground)))] bg-[length:250%_100%] bg-clip-text text-transparent">
                  Tu Vida,
                </span>
              </span>{" "}
              <span className="relative inline-block">
                <span className="animate-shine-infinite bg-[linear-gradient(110deg,hsl(var(--primary)),45%,hsl(var(--primary-foreground)),55%,hsl(var(--primary)))] bg-[length:250%_100%] bg-clip-text text-transparent">
                  Organizada.
                </span>
              </span>
              <br />
              <span className="relative inline-block">
                <span className="animate-shine-infinite bg-[linear-gradient(110deg,hsl(var(--foreground)),45%,hsl(var(--primary)),55%,hsl(var(--foreground)))] bg-[length:250%_100%] bg-clip-text text-transparent">
                  Tus Finanzas,
                </span>
              </span>{" "}
              <span className="relative inline-block">
                <span className="animate-shine-infinite bg-[linear-gradient(110deg,hsl(var(--primary)),45%,hsl(var(--primary-foreground)),55%,hsl(var(--primary)))] bg-[length:250%_100%] bg-clip-text text-transparent">
                  Optimizadas.
                </span>
              </span>
            </h1>
            <p className={cn("mt-6 max-w-xl text-lg text-foreground/80 transition-all duration-1000 ease-out", mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8")} style={{ transitionDelay: '200ms' }}>
              La plataforma con IA que unifica tus finanzas y tareas para que alcances tus metas sin esfuerzo. Deja de hacer malabares entre apps y toma el control real.
            </p>
            <div className={cn("mt-8 transition-all duration-1000 ease-out", mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8")} style={{ transitionDelay: '400ms' }}>
              <Button asChild size="lg" className="font-bold text-lg text-primary-foreground animate-scale-pulse bg-primary transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-primary/40 hover:bg-gradient-to-r from-primary to-primary/40">
                <Link href="#early-access" onClick={handleScroll}>QUIERO ACCESO ANTICIPADO</Link>
              </Button>
            </div>
          </div>
          <div className="relative flex items-center justify-center [perspective:1000px]">
            <InteractiveDashboard />
          </div>
        </div>
      </div>
    </section>
  );
}
