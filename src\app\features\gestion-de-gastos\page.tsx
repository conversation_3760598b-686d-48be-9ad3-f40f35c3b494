
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { CheckCircle2, TrendingUp, <PERSON>gyBank, <PERSON><PERSON><PERSON><PERSON>, BellRing, ListChecks } from 'lucide-react';

const GestionDeGastosContent = () => (
    <div className="space-y-8 mt-8">
        <h2 className="text-3xl font-bold mb-6 text-primary">Funcionalidades Detalladas:</h2>
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center"><CheckCircle2 className="mr-3 text-primary h-7 w-7 animate-bounce" /> Ingreso y Edición Flexible</h3>
            <ul className="list-disc list-inside">
                <li className="text-lg text-foreground/90">
                    Los usuarios podrán establecer un presupuesto mensual global en pesos chilenos (CLP) de manera sencilla. Esta funcionalidad incluirá opciones para editar el monto en cualquier momento del mes, adaptándose a cambios inesperados en los ingresos o gastos previstos.
                </li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center"><BellRing className="mr-3 text-primary h-7 w-7 animate-bounce" /> Alertas y Notificaciones</h3>
            <ul className="list-disc list-inside">
              <li className="text-lg text-foreground/90 mb-4">
                  En nuestro sistema esta implementado un sistema de alertas visuales o notificaciones cuando el usuario se acerque o exceda un porcentaje predefinido de su presupuesto total, fomentando una gestión proactiva.
              </li>
            </ul>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-base">
                <div className="bg-card/50 p-4 rounded-lg border">
                    <h4 className="font-semibold mb-2">Ejemplos de Categorías Automáticas:</h4>
                    <ul className="list-disc list-inside space-y-1 text-foreground/80">
                        <li>Alimentación (Supermercado, Restaurantes)</li>
                        <li>Transporte (Gasolina, Transporte Público)</li>
                        <li>Ocio (Cine, Conciertos, Libros)</li>
                        <li>Hogar (Arriendo, Servicios Básicos)</li>
                        <li>Suscripciones (Streaming, Software)</li>
                    </ul>
                </div>
                <div className="bg-card/50 p-4 rounded-lg border">
                     <h4 className="font-semibold mb-2">Beneficios de la Categorización:</h4>
                    <ul className="list-disc list-inside space-y-1 text-foreground/80">
                        <li>Identifica áreas de ahorro potencial.</li>
                        <li>Entiende tus hábitos de consumo.</li>
                        <li>Facilita la declaración de impuestos.</li>
                        <li>Planifica con datos reales.</li>
                    </ul>
                </div>
            </div>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center"><PiggyBank className="mr-3 text-primary h-7 w-7 animate-bounce" /> Control y Presupuestos</h3>
            <p className="text-lg text-foreground/90">
                Se implementará un formulario de registro de gastos diseñado con un enfoque en la usabilidad y la velocidad. Este formulario permitirá a los usuarios ingresar sus gastos de manera sencilla y rápida, minimizando el tiempo y el esfuerzo requeridos. Su diseño intuitivo garantizará que incluso los usuarios con poca experiencia tecnológica puedan completar el registro sin dificultad, facilitando así una gestión
            </p>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center"><ListChecks className="mr-3 text-primary h-7 w-7 animate-bounce" /> Listado de Gastos</h3>
            <div className="space-y-4 text-lg text-foreground/90">
                <div>
                    <h4 className="font-semibold text-xl mb-2">Visualización Clara y Organizada:</h4>
                    <ul className="list-disc list-inside ml-4 space-y-1 text-foreground/80">
                        <li>Todos los gastos registrados se presentarán en un formato tabular o de lista, garantizando una fácil lectura y comprensión.</li>
                        <li><strong>Opciones de Ordenamiento:</strong> Los usuarios podrán ordenar el listado por fecha (ascendente/descendente), monto (mayor/menor), o categoría.</li>
                        <li><strong>Opciones de Filtrado:</strong> Se incluirán filtros por categoría, rango de fechas y/o palabras clave en la descripción.</li>
                    </ul>
                </div>
                 <div>
                    <h4 className="font-semibold text-xl mb-2">Resumen de Gastos:</h4>
                     <ul className="list-disc list-inside ml-4 space-y-1 text-foreground/80">
                        <li><strong>Comparativa Visual Inmediata:</strong> Una de las características clave es la comparativa visual entre el presupuesto y el gasto.</li>
                        <li><strong>Indicadores de Color Intuitivos:</strong> Se usarán colores (verde, amarillo, rojo) para una retroalimentación instantánea sobre el estado financiero.</li>
                    </ul>
                </div>
                 <div>
                    <h4 className="font-semibold text-xl mb-2">Gráfico de Gastos:</h4>
                     <ul className="list-disc list-inside ml-4 space-y-1 text-foreground/80">
                        <li><strong>Representación Gráfica de Distribución:</strong> Se generará una representación visual de la distribución de gastos por categoría.</li>
                        <li><strong>Tipos de Gráficos:</strong> Se ofrecerán gráficos de torta o de barras para identificar rápidamente dónde se concentra el gasto.</li>
                    </ul>
                </div>
                 <p>Este análisis visual es fundamental para identificar áreas de ahorro y optimizar el presupuesto.</p>
            </div>
        </section>
        
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center"><ShieldCheck className="mr-3 text-primary h-7 w-7 animate-bounce" /> Seguridad a Nivel Bancario</h3>
            <ul className="list-disc list-inside">
                <li className="text-lg text-foreground/90">
                    Tu seguridad es nuestra máxima prioridad. Utilizamos encriptación de extremo a extremo y los mismos protocolos de seguridad que los grandes bancos para garantizar que tu información financiera esté siempre protegida.
                </li>
            </ul>
        </section>
    </div>
);

export default function GestionDeGastosPage() {
    const title = "Gestión de Gastos";

    return (
        <div className="container mx-auto px-4 py-12">
            <Card>
                <CardHeader>
                    <CardTitle className="text-3xl lg:text-5xl font-extrabold tracking-tight">
                        {title}
                    </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                    <p className="text-lg text-foreground/90">
                        Este módulo integral está diseñado para proporcionar a los usuarios una herramienta robusta y amigable para la administración y el control de sus finanzas personales, centrándose específicamente en la gestión de gastos mensuales. Su objetivo principal es facilitar una visión clara y detallada del flujo de dinero, permitiendo una toma de decisiones informada y una mejor salud financiera.
                    </p>
                    <GestionDeGastosContent />
                </CardContent>
            </Card>
        </div>
    );
}
