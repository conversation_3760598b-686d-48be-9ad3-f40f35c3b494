"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "lucide-react";

export function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    try {
      const consent = localStorage.getItem("cookie_consent");
      if (consent === null) {
        setIsVisible(true);
      }
    } catch (error) {
      console.error("Could not access localStorage:", error);
    }
  }, []);

  const handleConsent = (consent: "accepted" | "declined") => {
    try {
      localStorage.setItem("cookie_consent", consent);
      setIsVisible(false);
    } catch (error) {
      console.error("Could not access localStorage:", error);
    }
  };

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50 p-4 transition-transform duration-500 ease-in-out",
        isVisible ? "translate-y-0" : "translate-y-full"
      )}
    >
      <Card className="container mx-auto bg-card/80 backdrop-blur-sm border-border shadow-2xl shadow-primary/10">
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-start gap-4">
              <Cookie className="h-8 w-8 text-primary flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-bold text-lg">Este sitio web utiliza cookies</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Usamos cookies para mejorar tu experiencia, analizar el tráfico y personalizar contenido. Al hacer clic en &quot;Aceptar&quot;, aceptas nuestro uso de cookies. Lee nuestra{" "}
                  <Link href="/cookie-policy" className="underline hover:text-primary">
                    Política de Cookies
                  </Link>.
                </p>
              </div>
            </div>
            <div className="flex-shrink-0 flex gap-2 mt-4 md:mt-0">
              <Button
                variant="outline"
                onClick={() => handleConsent("declined")}
              >
                Rechazar
              </Button>
              <Button
                onClick={() => handleConsent("accepted")}
                className="bg-primary hover:bg-primary/90"
              >
                Aceptar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
