import type {Metadata} from 'next';
import { Manrope, Orbitron } from 'next/font/google';
import './globals.css';
import { Toaster } from "@/components/ui/toaster"
import './neuronal-menu.css';
import { NeuronalBackground } from '@/components/landing/neuronal-background';

const manrope = Manrope({
  subsets: ['latin'],
  variable: '--font-manrope',
  weight: ['400', '500', '600', '700', '800'],
});

const orbitron = Orbitron({
  subsets: ['latin'],
  variable: '--font-orbitron',
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: 'Asistente Integral 360',
  description: 'Tu Vida, Organizada. Tus Finanzas, Optimizadas.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" className={`dark ${manrope.variable} ${orbitron.variable}`}>
      <body className="font-body antialiased">
        <NeuronalBackground />
        <div className="relative z-10">
          {children}
        </div>
        <Toaster />
      </body>
    </html>
  );
}
