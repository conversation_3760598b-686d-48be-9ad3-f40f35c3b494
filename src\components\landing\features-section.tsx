
"use client";

import { Wallet, ShoppingCart, Repeat, Users, Search, Bot } from "lucide-react";
import { FadeIn } from "./fade-in";
import { FeatureCard } from "./feature-card";
import Link from "next/link";

const features = [
  {
    icon: Wallet,
    title: "Gestión de Gastos",
    description: "Categoriza tus gastos automáticamente y obtén una visión clara de a dónde va tu dinero."
  },
  {
    icon: ShoppingCart,
    title: "Listas de Compras Inteligentes",
    description: "Crea y comparte listas de compras que aprenden tus hábitos y te sugieren artículos."
  },
  {
    icon: Repeat,
    title: "Pagos Recurrentes",
    description: "Nunca más te olvides de un pago. Programamos y te recordamos tus facturas y suscripciones."
  },
  {
    icon: Users,
    title: "Gastos Comunes",
    description: "Divide cuentas con amigos y familiares de forma sencilla y sin complicaciones."
  },
  {
    icon: Search,
    title: "Monitor de Precios",
    description: "Sigue el precio de productos que te interesan y recibe alertas cuando bajen de precio."
  },
  {
    icon: <PERSON><PERSON>,
    title: "Asistente Personal",
    description: "Tu IA personal que te ayuda a planificar tu día, establecer metas y mantenerte enfocado."
  },
];

const slugify = (text: string) => {
  return text
    .toString()
    .normalize('NFD') // Normaliza los caracteres con tilde (ej: ó -> o´)
    .replace(/[\u0300-\u036f]/g, '') // Elimina los diacríticos (acentos)
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Reemplaza espacios con -
    .replace(/[^\w-]+/g, '') // Elimina todos los caracteres que no sean palabras o guiones
    .replace(/--+/g, '-'); // Reemplaza múltiples - con uno solo
}


export function FeaturesSection() {
  const getDirection = (index: number) => {
    const col = index % 3;
    if (col === 0) return 'left';
    if (col === 1) return 'up';
    return 'right';
  };

  return (
    <section id="features" className="py-20 sm:py-32 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold tracking-tighter sm:text-4xl md:text-5xl">
            <span className="animate-shine-infinite animate-scale-pulse bg-[linear-gradient(110deg,hsl(var(--primary)),45%,hsl(var(--accent)),55%,hsl(var(--primary)))] bg-[length:250%_100%] bg-clip-text text-transparent">
              Todo lo que necesitas, en un solo lugar
            </span>
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
            Descubre las herramientas que transformarán tu manera de organizarte y gestionar tus finanzas.
          </p>
        </div>
        <div className="mt-12 grid grid-flow-row-dense grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, i) => (
            <FadeIn 
              key={i} 
              direction={getDirection(i)}
              className="transition-all"
              style={{ transitionDelay: `${i * 100}ms`}}
            >
              <Link href={`/features/${slugify(feature.title)}`}>
                <FeatureCard feature={feature} isLink={true} />
              </Link>
            </FadeIn>
          ))}
        </div>
      </div>
    </section>
  );
}
