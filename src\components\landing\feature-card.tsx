"use client";

import type { LucideIcon } from "lucide-react";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

export interface Feature {
  icon: LucideIcon;
  title: string;
  description: string;
  content?: React.ReactNode;
}

interface FeatureCardProps {
  feature: Feature;
  isLink?: boolean;
  className?: string;
}

export function FeatureCard({ feature, isLink = false, className }: FeatureCardProps) {
  const { icon: Icon, title, description, content } = feature;

  return (
    <div className={cn(isLink ? "group h-full cursor-pointer" : "", className)}>
      <Card className="h-full bg-card/50 hover:bg-card/80 transition-all duration-300 ease-in-out flex flex-col hover:-translate-y-2 min-h-[240px]">
        <CardHeader className="p-6">
          <div className="mb-4">
            <Icon className="h-10 w-10 text-primary transition-transform duration-300 ease-in-out animate-scale-pulse" />
          </div>
          <CardTitle>{title}</CardTitle>
          <CardDescription className="pt-2 text-foreground/90">{description}</CardDescription>
        </CardHeader>
        {content && <CardContent className="pt-0 text-foreground/90">{content}</CardContent>}
        {isLink && (
            <div className="p-4 pt-0 mt-auto">
                <div className="flex items-center justify-end text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span>Click para saber más</span>
                    <ArrowRight className="h-3 w-3 ml-1" />
                </div>
            </div>
        )}
      </Card>
    </div>
  );
}