import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { BrainCir<PERSON>it, ListChecks, Recycle, Calendar<PERSON><PERSON>, ClipboardList, ClipboardCheck, ListFilter, Share2 } from "lucide-react";

const ListasDeComprasContent = () => (
    <div className="space-y-8 mt-8">
        <h2 className="text-3xl font-bold mb-6 text-primary">Secciones Diferenciadas y Especializadas:</h2>
        
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <BrainCircuit className="mr-3 text-primary h-7 w-7 animate-bounce" /> Lista de Supermercado
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>Una sección dedicada y organizada para artículos de supermercado, facilitando la navegación y adición de productos específicos de este tipo de establecimiento.</li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <ListChecks className="mr-3 text-primary h-7 w-7 animate-bounce" /> Lista de Feria/Mercado
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>Una sección independiente, pensada para productos frescos, frutas, verduras y otros artículos típicos de mercados y ferias, permitiendo una clasificación más intuitiva y eficiente.</li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <Recycle className="mr-3 text-primary h-7 w-7 animate-bounce" /> Sistema de Sugerencias Basado en el Historial y la Frecuencia
            </h3>
             <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>El sistema analizará el historial de compras y la frecuencia de adquisición para sugerir productos que podrían estar agotándose.</li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <CalendarClock className="mr-3 text-primary h-7 w-7 animate-bounce" /> Planificación y Programación de Compras Futuras
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>Se podrán programar recordatorios para compras recurrentes (ej. “Comprar leche cada 3 días”).</li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <ClipboardList className="mr-3 text-primary h-7 w-7 animate-bounce" /> Gestión Dinámica y Completa de Ítems
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li><strong>Añadir Ítems:</strong> Posibilidad de incorporar nuevos productos a cualquiera de las listas con facilidad, especificando detalles como cantidad, unidad de medida, o incluso notas adicionales.</li>
                <li><strong>Editar Ítems:</strong> Capacidad de modificar cualquier detalle de los productos ya agregados, desde la cantidad hasta la descripción, permitiendo una adaptabilidad total a las necesidades cambiantes.</li>
                <li><strong>Eliminar Ítems:</strong> Función para remover productos de las listas de manera sencilla, manteniendo las listas actualizadas y relevantes.</li>
            </ul>
        </section>
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <ClipboardCheck className="mr-3 text-primary h-7 w-7 animate-bounce" /> Marcado Intuitivo de Ítems (Casillas de Verificación/Tickets)
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>Implementación de casillas de verificación interactivas junto a cada ítem de la lista.</li>
                <li>Permite marcar visualmente los productos que han sido comprados, seleccionados o revisados, ofreciendo un seguimiento claro del progreso.</li>
                <li>Facilita la identificación rápida de los elementos pendientes y completados.</li>
            </ul>
        </section>
         <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <ListFilter className="mr-3 text-primary h-7 w-7 animate-bounce" /> Generación de Lista Filtrada y Consolidada
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li>Una funcionalidad clave que permite la creación de una nueva vista o informe. Esta opción filtra y presenta exclusivamente los ítems que han sido previamente marcados con la casilla de verificación. Ideal para revisar rápidamente los productos adquiridos, o para consolidar una lista de artículos "listos" para alguna acción específica (ej. empaquetar, despachar).</li>
            </ul>
        </section>
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <Share2 className="mr-3 text-primary h-7 w-7 animate-bounce" /> Acciones Versátiles y Colaborativas de la Lista
            </h3>
            <ul className="list-disc list-inside space-y-2 text-foreground/90">
                <li><strong>Imprimir:</strong> Opción para generar una versión imprimible de la lista (disponible desde interfaces web o de escritorio), útil para quienes prefieren una copia física.</li>
                <li><strong>Compartir:</strong> Funcionalidad para compartir la lista con otros usuarios o dispositivos. Esto puede realizarse a través de un enlace compartible, exportando la lista como texto plano, o integrándose con otras plataformas de mensajería/colaboración.</li>
                <li><strong>Copiar al Portapapeles:</strong> Permite copiar rápidamente el contenido de la lista al portapapeles del dispositivo, facilitando su pegado en otras aplicaciones, correos electrónicos o documentos.</li>
            </ul>
        </section>
    </div>
);


export default function ListasDeComprasInteligentesPage() {
    const title = "Listas de Compras Inteligentes";

    return (
        <div className="container mx-auto px-4 py-12">
            <Card>
                <CardHeader>
                    <CardTitle className="text-3xl lg:text-5xl font-extrabold tracking-tight">
                        {title}
                    </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                    <p className="text-lg text-foreground/90">Este módulo integral está meticulosamente diseñado para revolucionar la experiencia de planificación y ejecución de compras, ofreciendo un conjunto de funcionalidades detalladas, altamente personalizables y orientadas a la eficiencia. Su objetivo principal es optimizar cada etapa del proceso de adquisición, desde la identificación de necesidades hasta la entrega final y el seguimiento post-compra, garantizando una gestión de recursos más inteligente y estratégica.</p>
                    <ListasDeComprasContent />
                </CardContent>
            </Card>
        </div>
    );
}
