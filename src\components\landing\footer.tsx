"use client";

import Link from "next/link";

export function Footer() {
  const footerLinks = [
    { name: "Política de Privacidad", href: "/privacy-policy" },
    { name: "Política de Cookies", href: "/cookie-policy" },
    { name: "T<PERSON>rminos de Servicio", href: "/terms-of-service" },
  ];

  const handleCookiePrefs = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    // In a real app, this would open a cookie consent management modal.
    // For now, we can just log it or link to the policy.
    alert("Aquí podrías gestionar tus preferencias de cookies.");
  };

  return (
    <footer className="bg-background">
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold">Asistente Integral 360</h3>
            <p className="mt-2 text-muted-foreground">
              Control total para tu dinero, tu tiempo y tus sueños.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-12">
            <div>
              <h4 className="font-semibold text-primary-foreground/90">Domina tus Finanzas:</h4>
              <p className="mt-2 text-muted-foreground">
                Unifica cuentas, optimiza gastos y alcanza tus metas de ahorro.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-primary-foreground/90">Organiza tu Día a Día:</h4>
              <p className="mt-2 text-muted-foreground">
                Gestiona tareas y proyectos, planifica tu calendario y ten a mano tus recetas de cocina favoritas en un solo lugar.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-primary-foreground/90">Potencia tu Crecimiento con IA:</h4>
              <p className="mt-2 text-muted-foreground">
                Planifica tus vacaciones soñadas con itinerarios y sugerencias, guarda los consejos expertos de nuestra IA para mejorar en tus finanzas, productividad y más.
              </p>
            </div>
          </div>
        </div>
      </div>
      <section className="bg-secondary/30 py-8">
        <div className="container mx-auto px-4">
          <div className="border-t border-border/40 pt-8">
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 text-sm mb-4">
              {footerLinks.map((link) => (
                <Link key={link.name} href={link.href} className="text-muted-foreground hover:text-foreground">
                  {link.name}
                </Link>
              ))}
               <button onClick={handleCookiePrefs} className="text-muted-foreground hover:text-foreground text-sm">
                  Preferencias de Cookies
                </button>
            </div>
            <p className="text-center text-sm text-muted-foreground">
              © {new Date().getFullYear()} Asistente Integral 360. Todos los derechos reservados.
            </p>
          </div>
        </div>
      </section>
    </footer>
  );
}