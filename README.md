# 🤖 Asistente Integral 360

**Tu Vida, Organizada. Tus Finanzas, Optimizadas.**

Una landing page moderna y responsiva para el Asistente Integral 360, una aplicación de gestión financiera y organización personal con inteligencia artificial integrada.

## 🌟 Características

- **Diseño Moderno**: Interfaz dark mode con efectos visuales neurales
- **Totalmente Responsivo**: Optimizado para todos los dispositivos
- **Componentes Interactivos**: Dashboard 3D con animaciones fluidas
- **Formulario de Acceso Anticipado**: Sistema de suscripción para early access
- **Optimizado para SEO**: Meta tags y estructura optimizada
- **TypeScript**: Desarrollo type-safe con TypeScript
- **Tailwind CSS**: Estilos utilitarios y sistema de diseño consistente

## 🛠️ Stack Tecnológico

- **Framework**: [Next.js 15.3.3](https://nextjs.org/) con App Router
- **Lenguaje**: [TypeScript](https://www.typescriptlang.org/)
- **Estilos**: [Tailwind CSS](https://tailwindcss.com/)
- **Componentes UI**: [shadcn/ui](https://ui.shadcn.com/) + [Radix UI](https://www.radix-ui.com/)
- **Iconos**: [Lucide React](https://lucide.dev/)
- **Gráficos**: [Recharts](https://recharts.org/)
- **IA**: [Google Genkit](https://firebase.google.com/docs/genkit)
- **Hosting**: [Firebase App Hosting](https://firebase.google.com/docs/app-hosting)

## 🚀 Inicio Rápido

### Prerrequisitos

- Node.js 18.0 o superior
- npm, yarn, o pnpm

### Instalación

1. **Clona el repositorio**
   ```bash
   git clone https://github.com/tu-usuario/landing360.git
   cd landing360
   ```

2. **Instala las dependencias**
   ```bash
   npm install
   # o
   yarn install
   # o
   pnpm install
   ```

3. **Configura las variables de entorno**
   ```bash
   cp .env.example .env.local
   ```

   Edita `.env.local` y configura las variables necesarias (ver sección de Configuración).

4. **Ejecuta el servidor de desarrollo**
   ```bash
   npm run dev
   ```

5. **Abre tu navegador**

   Visita [http://localhost:9002](http://localhost:9002) para ver la aplicación.

## ⚙️ Configuración de Variables de Entorno

### Variables Requeridas

```env
# Google AI para funcionalidad de IA
GOOGLE_API_KEY=tu_clave_api_de_google

# Configuración básica
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

### Variables Opcionales

```env
# Firebase (si se usa)
NEXT_PUBLIC_FIREBASE_API_KEY=tu_firebase_api_key
NEXT_PUBLIC_FIREBASE_PROJECT_ID=tu_project_id

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Email service
EMAIL_SERVICE_API_KEY=tu_email_api_key
```

Para obtener una clave de Google AI:
1. Visita [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Crea una nueva clave API
3. Cópiala en tu archivo `.env.local`

## 📜 Scripts Disponibles

```bash
# Desarrollo
npm run dev              # Inicia servidor de desarrollo en puerto 9002
npm run genkit:dev       # Inicia Genkit AI en modo desarrollo
npm run genkit:watch     # Inicia Genkit AI con watch mode

# Construcción y producción
npm run build            # Construye la aplicación para producción
npm run start            # Inicia servidor de producción

# Calidad de código
npm run lint             # Ejecuta ESLint
npm run typecheck        # Verifica tipos de TypeScript
```

## 📁 Estructura del Proyecto

```
landing360/
├── src/
│   ├── app/                    # App Router de Next.js
│   │   ├── (legal)/           # Rutas agrupadas para páginas legales
│   │   ├── features/          # Páginas de características
│   │   ├── globals.css        # Estilos globales
│   │   ├── layout.tsx         # Layout principal
│   │   └── page.tsx           # Página de inicio
│   ├── components/
│   │   ├── landing/           # Componentes específicos del landing
│   │   └── ui/                # Componentes UI reutilizables (shadcn/ui)
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utilidades y configuraciones
│   └── ai/                    # Configuración de Genkit AI
├── docs/                      # Documentación del proyecto
├── public/                    # Archivos estáticos
├── .env.example              # Plantilla de variables de entorno
├── next.config.ts            # Configuración de Next.js
├── tailwind.config.ts        # Configuración de Tailwind CSS
└── tsconfig.json             # Configuración de TypeScript
```

## 🎨 Guía de Diseño

### Colores Principales

- **Primario**: Magenta (#E040FB) - Energía e innovación
- **Fondo**: Gris oscuro (#0A0A1A) - Experiencia dark mode moderna
- **Acento**: Cyan (#00BCD4) - Elementos interactivos y datos importantes

### Tipografía

- **Cuerpo**: Manrope (sans-serif) - Legibilidad y modernidad
- **Títulos**: Orbitron (sans-serif) - Carácter tecnológico

### Componentes Clave

- **Fondo Neuronal**: Animación de partículas conectadas
- **Dashboard Interactivo**: Efecto 3D flip con datos financieros
- **Formulario de Acceso**: Validación en tiempo real con Zod
- **Animaciones**: Scroll-triggered animations con Intersection Observer

## 🚀 Despliegue

### Firebase App Hosting (Recomendado)

1. **Instala Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Inicia sesión en Firebase**
   ```bash
   firebase login
   ```

3. **Configura el proyecto**
   ```bash
   firebase init hosting
   ```

4. **Construye y despliega**
   ```bash
   npm run build
   firebase deploy
   ```

### Vercel

1. **Conecta tu repositorio** en [vercel.com](https://vercel.com)
2. **Configura las variables de entorno** en el dashboard de Vercel
3. **Despliega automáticamente** con cada push a main

### Netlify

1. **Conecta tu repositorio** en [netlify.com](https://netlify.com)
2. **Configura el build command**: `npm run build`
3. **Configura el publish directory**: `.next`
4. **Añade las variables de entorno** en el dashboard

## 🔧 Configuración de Producción

### Variables de Entorno Críticas

```env
# Requeridas para producción
GOOGLE_API_KEY=tu_clave_api_real
NEXT_PUBLIC_APP_URL=https://tu-dominio.com
NODE_ENV=production
NEXTAUTH_SECRET=secreto_unico_y_seguro
```

### Optimizaciones de Rendimiento

- ✅ **Imágenes optimizadas** con Next.js Image
- ✅ **Lazy loading** automático de componentes
- ✅ **Code splitting** por rutas
- ✅ **Minificación** automática en build
- ✅ **Preconnect** a Google Fonts
- ✅ **Bundle analysis** disponible

### Headers de Seguridad

El proyecto incluye configuración de headers de seguridad en `next.config.ts`:
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy

## 🧪 Testing

### Ejecutar Tests (cuando se implementen)

```bash
# Tests unitarios
npm run test

# Tests con coverage
npm run test:coverage

# Tests e2e
npm run test:e2e
```

### Estructura de Testing Recomendada

```
tests/
├── __mocks__/              # Mocks para testing
├── components/             # Tests de componentes
├── pages/                  # Tests de páginas
└── utils/                  # Tests de utilidades
```

## 🐛 Debugging

### Logs de Desarrollo

```bash
# Habilitar logs detallados
DEBUG=true npm run dev

# Logs de Genkit AI
npm run genkit:dev
```

### Herramientas de Debug

- **React Developer Tools**: Para debugging de componentes
- **Next.js DevTools**: Para análisis de rendimiento
- **Tailwind CSS IntelliSense**: Para autocompletado de clases

## 📈 Monitoreo y Analytics

### Google Analytics

1. Obtén un Measurement ID en [Google Analytics](https://analytics.google.com)
2. Añade `NEXT_PUBLIC_GA_MEASUREMENT_ID` a tu `.env.local`
3. El tracking se activará automáticamente

### Error Tracking con Sentry (Opcional)

1. Crea una cuenta en [Sentry](https://sentry.io)
2. Obtén tu DSN
3. Añade `SENTRY_DSN` a tus variables de entorno

## 🤝 Contribución

### Flujo de Desarrollo

1. **Fork** el repositorio
2. **Crea una rama** para tu feature: `git checkout -b feature/nueva-funcionalidad`
3. **Commit** tus cambios: `git commit -m 'Añade nueva funcionalidad'`
4. **Push** a la rama: `git push origin feature/nueva-funcionalidad`
5. **Abre un Pull Request**

### Estándares de Código

- **TypeScript**: Tipado estricto requerido
- **ESLint**: Seguir las reglas configuradas
- **Prettier**: Formateo automático
- **Conventional Commits**: Para mensajes de commit consistentes

### Antes de Hacer Commit

```bash
# Verificar tipos
npm run typecheck

# Verificar linting
npm run lint

# Construir para verificar errores
npm run build
```

## 📞 Soporte

### Problemas Comunes

**Error de build con TypeScript**
```bash
# Limpiar cache y reinstalar
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

**Variables de entorno no funcionan**
- Verifica que el archivo `.env.local` existe
- Asegúrate de que las variables públicas empiecen con `NEXT_PUBLIC_`
- Reinicia el servidor de desarrollo después de cambios

**Problemas con Genkit AI**
- Verifica que `GOOGLE_API_KEY` esté configurada
- Revisa los logs con `npm run genkit:dev`

### Contacto

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/tu-usuario/landing360/issues)
- **Documentación**: [Wiki del proyecto](https://github.com/tu-usuario/landing360/wiki)

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

## 🙏 Agradecimientos

- [Next.js](https://nextjs.org/) por el framework increíble
- [Tailwind CSS](https://tailwindcss.com/) por el sistema de diseño
- [shadcn/ui](https://ui.shadcn.com/) por los componentes hermosos
- [Lucide](https://lucide.dev/) por los iconos
- [Firebase](https://firebase.google.com/) por el hosting y servicios

---

**Desarrollado con ❤️ para optimizar tu vida financiera y personal**
