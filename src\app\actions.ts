"use server";

import { z } from "zod";

const subscribeSchema = z.object({
  email: z.string().email({ message: "Por favor, introduce un correo electrónico válido." }),
});

type State = {
  message: string;
  success: boolean;
} | null;

export async function subscribeToEarlyAccess(prevState: State, formData: FormData): Promise<State> {
  const validatedFields = subscribeSchema.safeParse({
    email: formData.get("email"),
  });

  if (!validatedFields.success) {
    return {
      message: validatedFields.error.errors[0].message,
      success: false,
    };
  }

  const email = validatedFields.data.email;

  console.log(`Subscribing email: ${email}`);

  await new Promise(resolve => setTimeout(resolve, 1500));

  if (email === '<EMAIL>') {
    return { message: 'Este correo ya está registrado.', success: false };
  }

  return { message: '¡Gracias por registrarte! Te hemos añadido a la lista de acceso anticipado.', success: true };
}
