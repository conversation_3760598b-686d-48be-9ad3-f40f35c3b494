{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "react/no-unescaped-entities": "off", "react/display-name": "off", "react-hooks/exhaustive-deps": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "@next/next/no-img-element": "error", "@next/next/no-html-link-for-pages": "error"}, "ignorePatterns": ["node_modules/**", ".next/**", "out/**", "build/**", "dist/**", "*.config.js", "*.config.ts"]}