import { <PERSON>, <PERSON>Header, CardTitle, CardDescription } from "@/components/ui/card";
import { ShieldCheck, Layers, Lock, FileText } from "lucide-react";
import { FadeIn } from "./fade-in";

const securityPillars = [
  {
    icon: ShieldCheck,
    title: "Autenticación Segura",
    description: "Protegemos tu cuenta con los más altos estándares de autenticación multifactor."
  },
  {
    icon: Layers,
    title: "Protección Multicapa",
    description: "Nuestra infraestructura cuenta con múltiples capas de seguridad para defender tus datos."
  },
  {
    icon: Lock,
    title: "Comunicación Encriptada",
    description: "Toda la información viaja encriptada de extremo a extremo, desde tu dispositivo hasta nuestros servidores."
  },
  {
    icon: FileText,
    title: "Auditoría Completa",
    description: "Realizamos auditorías de seguridad periódicas para garantizar la máxima protección."
  }
];

export function SecuritySection() {
  return (
    <section id="security" className="py-20 sm:py-32 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold tracking-tighter sm:text-4xl md:text-5xl">TU <span className="text-primary">SEGURIDAD</span> ES NUESTRA PRIORIDAD</h2>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
            Hemos construido Asistente Integral 360 sobre una base de seguridad robusta para que puedas gestionar tu vida con total tranquilidad.
          </p>
        </div>
        <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {securityPillars.map((pillar, i) => (
            <FadeIn key={i} style={{ transitionDelay: `${i * 150}ms`}}>
              <Card className="relative h-full bg-background border-border/50 hover:border-primary/50 transition-colors">
                <CardHeader className="p-6">
                  <div className="mb-4">
                    <pillar.icon className="h-10 w-10 text-primary" />
                  </div>
                  <CardTitle>{pillar.title}</CardTitle>
                  <CardDescription className="pt-2">{pillar.description}</CardDescription>
                </CardHeader>
              </Card>
            </FadeIn>
          ))}
        </div>
      </div>
    </section>
  );
}
