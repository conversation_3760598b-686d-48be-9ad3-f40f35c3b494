
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ListTodo, Edit3, Trash2, CheckSquare, Calendar, CalendarClock, CalendarX2, BellRing, Clock, Volume2 } from "lucide-react";

export default function AsistentePersonalPage() {
    const title = "Asistente Personal";

    return (
        <div className="container mx-auto px-4 py-12">
            <Card>
                <CardHeader>
                    <CardTitle className="text-3xl lg:text-5xl font-extrabold tracking-tight">
                        {title}
                    </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                    <p className="text-lg text-foreground/90 mb-8">La propuesta de valor central de nuestro asistente integral se cimenta en tres pilares fundamentales que buscan optimizar la gestión de la productividad y la organización personal o profesional de manera fluida e intuitiva:</p>

                    <section>
                        <h2 className="text-3xl font-bold mb-6 text-primary">Gestión de Tareas Integral y Flexible:</h2>
                        <p className="text-lg text-foreground/90 mb-6">
                            El sistema ofrece una robusta funcionalidad para la administración de tareas, cubriendo todo el ciclo de vida de las mismas. Esto incluye:
                        </p>
                        <div className="space-y-6">
                            <div className="flex items-start gap-4">
                                <ListTodo className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Creación Detallada</h3>
                                    <p className="text-foreground/90">
                                        Permite a los usuarios definir tareas con una descripción clara y concisa. La asignación de una fecha y hora de vencimiento es obligatoria para garantizar la priorización y el seguimiento. Opcionalmente, se puede establecer un nivel de prioridad (alta, media, baja) para una clasificación adicional. Una característica distintiva es la configuración de repetición, que facilita la gestión de tareas recurrentes, ya sean diarias, semanales, mensuales o con patrones personalizados, eliminando la necesidad de crearlas manually de forma repetida.
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-start gap-4">
                                <Edit3 className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Edición Eficiente</h3>
                                    <p className="text-foreground/90">
                                        Los usuarios pueden modificar cualquier atributo de una tarea existente en cualquier momento, adaptándose a cambios en los plazos, prioridades o descripciones.
                                    </p>
                                </div>
                            </div>
                             <div className="flex items-start gap-4">
                                <Trash2 className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Eliminación Sencilla</h3>
                                    <p className="text-foreground/90">
                                        Facilita la remoción de tareas que ya no son relevantes o han sido canceladas.
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-start gap-4">
                                <CheckSquare className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Marcado como Completadas</h3>
                                    <p className="text-foreground/90">
                                        Una vez finalizada una tarea, los usuarios pueden marcarla como completada, lo que no solo la archiva visualmente, sino que también contribuye a un sentido de progreso y logro.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section className="mt-12">
                        <h2 className="text-3xl font-bold mb-6 text-primary">Visualización Organizada e Intuitiva:</h2>
                        <p className="text-lg text-foreground/90 mb-6">
                            Para combatir la sobrecarga de información y permitir una rápida comprensión del estado de las tareas, el asistente proporciona vistas organizadas que agrupan las tareas de manera lógica:
                        </p>
                        <div className="space-y-6">
                            <div className="flex items-start gap-4">
                                <Calendar className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Vista "Hoy"</h3>
                                    <p className="text-foreground/90">
                                       Presenta una lista clara y concisa de todas las tareas con vencimiento en el día actual, permitiendo a los usuarios enfocarse en sus prioridades inmediatas.
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-start gap-4">
                                <CalendarClock className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Vista "Próximos Días"</h3>
                                    <p className="text-foreground/90">
                                        Ofrece una perspectiva a futuro, mostrando las tareas programadas para los días venideros. Esta vista es crucial para la planificación proactiva y la anticipación de cargas de trabajo.
                                    </p>
                                </div>
                            </div>
                             <div className="flex items-start gap-4">
                                <CalendarX2 className="h-8 w-8 text-destructive mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Vista "Atrasadas"</h3>
                                    <p className="text-foreground/90">
                                        Destaca de forma prominente las tareas cuyo vencimiento ya ha pasado, sirviendo como un recordatorio visual urgente y fomentando la acción correctiva.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section className="mt-12">
                        <h2 className="text-3xl font-bold mb-6 text-primary">Sistema de Recordatorios Proactivo y Personalizable:</h2>
                        <p className="text-lg text-foreground/90 mb-6">
                            Más allá de una simple lista de tareas, el asistente incorpora un potente sistema de recordatorios diseñado para asegurar que ninguna tarea importante pase desapercibida. Este sistema es altamente configurable para adaptarse a las preferencias del usuario y sus necesidades específicas:
                        </p>
                        <div className="space-y-6">
                            <div className="flex items-start gap-4">
                                <BellRing className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Notificaciones Multiplataforma</h3>
                                    <p className="text-foreground/90 mb-3">
                                        Los recordatorios se emiten a través de diversos canales para garantizar su visibilidad, incluyendo:
                                    </p>
                                    <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                                        <li>Notificaciones web: Aparecen directamente en el navegador.</li>
                                        <li>Notificaciones push en móvil: Se envían al dispositivo móvil.</li>
                                        <li>Alertas en escritorio: Pop-ups o banners en el sistema operativo.</li>
                                    </ul>
                                </div>
                            </div>
                            <div className="flex items-start gap-4">
                                <Clock className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Configuración de Tiempo Flexible</h3>
                                     <p className="text-foreground/90 mb-3">
                                        Los usuarios tienen la libertad de configurar cuándo desean recibir el recordatorio:
                                    </p>
                                     <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                                        <li>En el momento del vencimiento.</li>
                                        <li>Con antelación (ej. 15 minutos, 1 hora, 1 día antes).</li>
                                    </ul>
                                </div>
                            </div>
                             <div className="flex items-start gap-4">
                                <Volume2 className="h-8 w-8 text-primary mt-1 flex-shrink-0 animate-bounce" />
                                <div>
                                    <h3 className="text-xl font-bold">Sonido Opcional</h3>
                                    <p className="text-foreground/90">
                                        Para aquellos que prefieren una alerta audible, el sistema permite activar un sonido de notificación para captar la atención del usuario.
                                    </p>
                                </div>
                            </div>
                        </div>
                         <p className="text-lg text-foreground/90 mt-8">En conjunto, estas características proporcionan una solución integral que va más allá de una simple agenda, ofreciendo una herramienta dinámica para la gestión efectiva del tiempo y las responsabilidades.</p>
                    </section>
                </CardContent>
            </Card>
        </div>
    );
}
