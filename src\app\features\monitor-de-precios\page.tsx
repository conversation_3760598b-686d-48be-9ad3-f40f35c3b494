
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { ShoppingCart, Leaf, HeartPulse, Cigarette, Edit, Bot } from "lucide-react";

const MonitorDePreciosContent = () => (
    <div className="space-y-8 mt-8">
        <h2 className="text-3xl font-bold mb-6 text-primary">Categorías de Seguimiento:</h2>
        
        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <ShoppingCart className="mr-3 text-primary h-7 w-7 animate-bounce" /> Supermercado
            </h3>
            <p className="text-lg text-foreground/90">
                Un espacio dedicado para la gestión de precios de productos de la canasta básica, abarrotes, lácteos, carnes, etc. Permite al usuario comparar precios entre diferentes establecimientos y realizar compras más inteligentes.
            </p>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <Leaf className="mr-3 text-primary h-7 w-7 animate-bounce" /> Feria
            </h3>
            <p className="text-lg text-foreground/90">
                Ideal para quienes adquieren productos frescos en mercados locales. Aquí se pueden registrar precios de frutas, verduras, hortalizas, y otros productos de temporada, facilitando la identificación de las mejores ofertas.
            </p>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <HeartPulse className="mr-3 text-primary h-7 w-7 animate-bounce" /> Remedios
            </h3>
            <p className="text-lg text-foreground/90">
                Una sección vital para el seguimiento de precios de medicamentos de venta libre y bajo receta. Esto es crucial para el control de gastos de salud y la búsqueda de opciones más económicas sin comprometer la calidad.
            </p>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <Cigarette className="mr-3 text-primary h-7 w-7 animate-bounce" /> Cigarros
            </h3>
            <p className="text-lg text-foreground/90">
                Un apartado específico para el monitoreo de precios de productos de tabaco, útil para usuarios que desean llevar un registro detallado de sus gastos en esta categoría.
            </p>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center">
                <Edit className="mr-3 text-primary h-7 w-7 animate-bounce" /> Campos Editables
            </h3>
            <p className="text-lg text-foreground/90 mb-4">
                Cada entrada en estas secciones es totalmente editable, permitiendo al usuario modificar los siguientes campos:
            </p>
            <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                <li><strong>Producto:</strong> Nombre exacto del artículo.</li>
                <li><strong>Precio:</strong> Valor monetario del producto en el momento de la adquisición.</li>
                <li><strong>Fecha de Actualización:</strong> Permite llevar un registro cronológico de los precios, facilitando el análisis de tendencias y variaciones a lo largo del tiempo.</li>
            </ul>
        </section>

        <section>
            <h3 className="text-2xl font-bold mb-3 flex items-center text-accent">
                <Bot className="mr-3 text-accent h-7 w-7" /> Automatización de Precios (<span className="animate-scale-pulse inline-block">(  Funcionalidad Avanzada  )</span>)
            </h3>
            <div className="text-lg text-foreground/90 space-y-4">
                <p>
                    Esta es una característica de vanguardia que eleva el Módulo 5 a un nivel superior, ofreciendo capacidades de recolección de datos automatizada para un análisis de precios en tiempo real y a gran escala.
                </p>
                <ul className="list-disc list-inside space-y-3 text-foreground/90 pl-4">
                    <li>
                        <strong>Sistema de Recolección Automatizada:</strong> La funcionalidad principal de esta sección es la capacidad de intentar obtener precios de productos específicos directamente desde sitios web de supermercados y farmacias. Esto elimina la necesidad de la entrada manual de datos y proporciona información actualizada al instante.
                    </li>
                    <li>
                        <strong>Panel de Configuración de URLs:</strong> Un panel de control centralizado y fácil de usar donde los usuarios pueden ingresar y gestionar las URLs (direcciones web) de los sitios de supermercados y farmacias de su interés. Este panel permitirá agregar, editar y eliminar URLs, asegurando que el sistema solo rastree las fuentes de información relevantes para el usuario.
                    </li>
                </ul>
                <p>
                    La combinación de listas de precios manuales y la automatización avanzada convierte al Módulo 5 en una herramienta indispensable para individuos y pequeñas empresas que buscan optimizar sus decisiones de compra, monitorear la competencia o simplemente llevar un control exhaustivo de sus gastos.
                </p>
            </div>
        </section>
    </div>
);


export default function MonitorDePreciosPage() {
    const title = "Monitor de Precios";

    return (
        <div className="container mx-auto px-4 py-12">
            <Card>
                <CardHeader>
                    <CardTitle className="text-3xl lg:text-5xl font-extrabold tracking-tight">
                        {title}
                    </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                    <p className="text-lg text-foreground/90">Este módulo fundamental está diseñado para ofrecer una solución integral para el seguimiento y la gestión de precios, adaptándose a las necesidades tanto de control manual como de automatización avanzada. Su objetivo principal es proporcionar a los usuarios una herramienta robusta para la optimización de sus compras y el análisis de mercado.</p>
                    <MonitorDePreciosContent />
                </CardContent>
            </Card>
        </div>
    );
}
