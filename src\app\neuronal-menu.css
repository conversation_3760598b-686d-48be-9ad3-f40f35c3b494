:root {
    --bg-color: #0a0a1a;
    --primary-glow: #00e5ff;
    --secondary-glow: #ff00e6;
    --accent-glow: #fffb00;
    --node-size: 60px;
    --icon-size: 24px;
    --spacing: 80px;
    --animation-speed: 0.5s;
    --pulse-speed: 1.5s;
    --trace-width: 4px;
}

.neuronal-menu {
    position: relative;
    width: var(--node-size);
    height: var(--node-size);
    display: flex;
    justify-content: center;
    transition: height var(--animation-speed) cubic-bezier(0.83, 0, 0.17, 1);
    z-index: 10;
}

/* --- STATE: ACTIVE/HOVER --- */
.neuronal-menu.is-active,
.neuronal-menu:hover {
    height: calc(var(--spacing) * 5 + var(--node-size));
}

.neuronal-menu.is-active .trace,
.neuronal-menu:hover .trace {
    height: calc(var(--spacing) * 5);
    transition-delay: 0.1s;
}

.neuronal-menu.is-active .share-button,
.neuronal-menu:hover .share-button {
    box-shadow: 0 0 15px 5px var(--secondary-glow), 0 0 5px 2px var(--secondary-glow) inset;
    animation: none;
}

.neuronal-menu.is-active .share-button::before,
.neuronal-menu:hover .share-button::before {
    animation: none;
    opacity: 0;
}

.neuronal-menu.is-active .share-icon,
.neuronal-menu:hover .share-icon {
    transform: rotate(360deg);
}

.neuronal-menu.is-active .social-icon,
.neuronal-menu:hover .social-icon {
    opacity: 1;
    transform: scale(1);
}

.neuronal-menu.is-active .social-icon-1,
.neuronal-menu:hover .social-icon-1 {
    transform: translateY(var(--spacing));
    transition-delay: 0.1s;
}
.neuronal-menu.is-active .social-icon-2,
.neuronal-menu:hover .social-icon-2 {
    transform: translateY(calc(var(--spacing) * 2));
    transition-delay: 0.2s;
}
.neuronal-menu.is-active .social-icon-3,
.neuronal-menu:hover .social-icon-3 {
    transform: translateY(calc(var(--spacing) * 3));
    transition-delay: 0.3s;
}
.neuronal-menu.is-active .social-icon-4,
.neuronal-menu:hover .social-icon-4 {
    transform: translateY(calc(var(--spacing) * 4));
    transition-delay: 0.4s;
}
.neuronal-menu.is-active .social-icon-5,
.neuronal-menu:hover .social-icon-5 {
    transform: translateY(calc(var(--spacing) * 5));
    transition-delay: 0.5s;
}


/* --- BUTTONS & ICONS --- */
.share-button {
    position: absolute;
    top: 0;
    width: var(--node-size);
    height: var(--node-size);
    border-radius: 50%;
    background-color: var(--bg-color);
    border: 2px solid var(--primary-glow);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 2;
    transition: box-shadow var(--animation-speed) ease;
    box-shadow: 0 0 15px 5px var(--primary-glow), 0 0 5px 2px var(--primary-glow) inset;
    animation: jump 2s infinite cubic-bezier(0.45, 0.05, 0.55, 0.95);
}

.share-button::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    border: 2px solid var(--primary-glow);
    animation: pulse var(--pulse-speed) infinite;
    opacity: 0;
}

.share-icon {
    width: calc(var(--icon-size) + 4px);
    height: calc(var(--icon-size) + 4px);
    color: var(--primary-glow);
    transition: transform var(--animation-speed) cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.social-icon {
    position: absolute;
    top: 0;
    width: var(--node-size);
    height: var(--node-size);
    opacity: 0;
    transform: scale(0.5);
    transition: transform var(--animation-speed) cubic-bezier(0.68, -0.55, 0.27, 1.55), opacity 0.3s ease;
    z-index: 1;
}

.social-icon a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--bg-color);
    border: 2px solid var(--primary-glow);
    box-shadow: 0 0 10px 2px var(--primary-glow) inset, 0 0 10px 3px var(--primary-glow);
    transition: all 0.3s ease;
}

.social-icon a:hover {
    background-color: var(--primary-glow);
    box-shadow: 0 0 20px 8px var(--primary-glow), 0 0 8px 3px var(--bg-color) inset;
}

.social-svg {
    width: var(--icon-size);
    height: var(--icon-size);
    color: var(--primary-glow);
    transition: all 0.3s ease;
}
.social-icon a:hover .social-svg {
    color: var(--bg-color);
    transform: scale(1.1);
}

/* --- TRACE --- */
.trace {
    position: absolute;
    top: calc(var(--node-size) / 2);
    left: 50%;
    transform: translateX(-50%);
    width: var(--trace-width);
    height: 0;
    background: linear-gradient(
        to bottom,
        var(--primary-glow),
        var(--secondary-glow) 80%
    );
    box-shadow: 0 0 10px 2px var(--primary-glow);
    border-radius: calc(var(--trace-width) / 2);
    transition: height var(--animation-speed) cubic-bezier(0.83, 0, 0.17, 1);
    z-index: 0;
}


@keyframes pulse {
    0% {
        transform: scale(0.9);
        opacity: 0.8;
    }
    70% {
        transform: scale(1.4);
        opacity: 0;
    }
    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

@keyframes jump {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}
