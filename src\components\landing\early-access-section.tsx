import { Badge } from "@/components/ui/badge";
import { EarlyAccessForm } from "@/components/landing/early-access-form";
import { CircleCheckBig, Check } from "lucide-react";

const benefits = [
    "Acceso prioritario a nuevas funciones",
    "Soporte premium durante el primer año",
    "Precios especiales de por vida",
    "Participación en el desarrollo del producto",
];

const subBenefits = [
    "Solo enviamos contenido relevante",
    "Tu email está 100% seguro",
    "Totalmente Gratis",
];

export function EarlyAccessSection() {
    return (
        <section id="early-access" className="py-20 sm:py-32">
            <div className="container mx-auto px-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="order-2 lg:order-1 text-left">
                        <h2 className="text-3xl font-extrabold tracking-tighter sm:text-4xl md:text-5xl">
                            <span className="animate-shine-infinite bg-[linear-gradient(110deg,hsl(var(--primary)),45%,hsl(var(--accent)),55%,hsl(var(--primary)))] bg-[length:250%_100%] bg-clip-text text-transparent">
                                Sé el primero en probar el futuro.
                            </span>
                        </h2>
                        <p className="mt-4 text-lg text-foreground/90">
                            Regístrate para el acceso anticipado y obtén beneficios
                            exclusivos. Ayúdanos a construir la mejor herramienta de
                            productividad y finanzas.
                        </p>
                        <ul className="mt-6 space-y-3">
                            {benefits.map((benefit, i) => (
                                <li key={i} className="flex items-center gap-3">
                                    <CircleCheckBig className="h-5 w-5 text-accent" />
                                    <span className="text-foreground/90">{benefit}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div className="order-1 lg:order-2">
                        <div className="relative bg-card/50 rounded-lg p-8 shadow-2xl shadow-primary/10 border border-border hover:border-destructive transition-colors">
                            <div className="absolute -top-28 left-1/2 -translate-x-1/2">
                                <Badge
                                    variant="outline"
                                    className="text-primary border-primary text-lg py-3 px-6 animate-scale-pulse"
                                >
                                    ACCESO ANTICIPADO
                                </Badge>
                            </div>
                            <h3 className="text-xl font-bold text-center mt-4 uppercase">
                                Únete a la lista de espera
                            </h3>
                            <p className="text-center text-muted-foreground mt-2">
                                Recibirás un correo cuando lancemos.
                            </p>
                            <div className="mt-6">
                                <EarlyAccessForm />
                            </div>
                            <div className="mt-6 space-y-2 text-sm text-muted-foreground">
                                {subBenefits.map((benefit, i) => (
                                     <div key={i} className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-green-500" />
                                        <span>{benefit}</span>
                                    </div>
                                ))}
                            </div>
                            <p className="text-center text-sm mt-4">
                                Cupos Limitados
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
