"use client";

import { useActionState, useEffect, useState, useRef } from "react";
import { useFormStatus } from "react-dom";
import { subscribeToEarlyAccess } from "@/app/actions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

function SubmitButton({ success }: { success: boolean }) {
  const { pending } = useFormStatus();
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    if (success) {
      setIsSuccess(true);
      const timer = setTimeout(() => {
        setIsSuccess(false);
      }, 7000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  return (
    <Button
      type="submit"
      className={cn(
        "w-full sm:w-auto font-bold flex-shrink-0 transition-colors duration-300 relative z-10",
        isSuccess && "bg-green-500 hover:bg-green-600 text-white"
      )}
      size="lg"
      disabled={pending || isSuccess}
    >
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Enviando...
        </>
      ) : isSuccess ? (
        "¡GRACIAS POR SUSCRIBIRTE!"
      ) : (
        "QUIERO ACCESO ANTICIPADO"
      )}
    </Button>
  );
}

export function EarlyAccessForm() {
  const [state, formAction] = useActionState(subscribeToEarlyAccess, null);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (state) {
      toast({
        title: state.success ? "¡Éxito!" : "Error",
        description: state.message,
        variant: state.success ? "default" : "destructive",
      });
      if (state.success) {
        formRef.current?.reset();
      }
    }
  }, [state, toast]);

  return (
    <form action={formAction} ref={formRef} className="mt-4">
      <div className="flex flex-col sm:flex-row items-center gap-3">
        <Input
          name="email"
          type="email"
          placeholder="<EMAIL>"
          required
          className="text-base h-12 flex-grow"
        />
        <SubmitButton success={state?.success ?? false} />
      </div>
    </form>
  );
}
