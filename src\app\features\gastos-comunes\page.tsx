
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { ListChecks, FileText, History, ThumbsUp, Lightbulb } from "lucide-react";

export default function GastosComunesPage() {
    const title = "Gastos Comunes";

    return (
        <div className="container mx-auto px-4 py-12">
            <Card>
                <CardHeader>
                    <CardTitle className="text-3xl lg:text-5xl font-extrabold tracking-tight">
                        {title}
                    </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                    <p className="text-lg text-foreground/90 mb-8">Un sistema de registro específico para los gastos compartidos o de comunidad, idealmente aplicable a diversos escenarios como condominios, edificios de oficinas, o incluso grupos de amigos compartiendo una vivienda.</p>

                    <h2 className="text-3xl font-bold mb-6 text-primary">Funcionalidades Clave:</h2>

                    <section>
                        <h3 className="text-2xl font-bold mb-4 flex items-center">
                            <ListChecks className="mr-3 text-primary h-7 w-7 animate-bounce" />
                            Registro Detallado de Pagos Fijos
                        </h3>
                        <ul className="list-disc list-inside space-y-3 text-foreground/90 pl-4">
                            <li><strong>Servicios Básicos:</strong> Incluye la capacidad de registrar pagos esenciales como luz, agua, gas e internet, facilitando la visualización de los gastos mensuales en estas categorías.</li>
                            <li><strong>Comunicaciones:</strong> Permite el seguimiento de pagos de teléfono (fijo y móvil), garantizando que las líneas se mantengan activas.</li>
                            <li><strong>Suscripciones y Membresías:</strong> Ideal para gestionar suscripciones a plataformas de streaming (Netflix, Spotify, etc.), gimnasios, software, revistas o cualquier otro servicio recurrente.</li>
                            <li><strong>Créditos y Préstamos:</strong> Aunque no es su función principal, puede adaptarse para registrar pagos de cuotas de préstamos o tarjetas de crédito, aunque un módulo financiero más avanzado sería idóneo para ello.</li>
                            <li><strong>Otros Pagos Recurrentes:</strong> Flexibilidad para añadir cualquier otro tipo de pago que se repita con regularidad, como alquiler, hipoteca, seguros, colegiaturas, etc.</li>
                        </ul>
                    </section>
                    
                    <section className="mt-8">
                        <h3 className="text-2xl font-bold mb-4 flex items-center">
                            <FileText className="mr-3 text-primary h-7 w-7 animate-bounce" />
                            Campos de Información Esenciales
                        </h3>
                        <ul className="list-disc list-inside space-y-3 text-foreground/90 pl-4">
                            <li><strong>Nombre del Servicio/Concepto:</strong> Un campo descriptivo para identificar claramente cada pago (ej. “Electricidad – Hogar”, “Internet – Fibra Óptica”, “Suscripción Netflix”).</li>
                            <li><strong>Monto (Pesos Chilenos - CLP):</strong> El valor exacto del pago, esencial para la planificación presupuestaria. Se podría incluir una opción para pagos con montos variables (ej. consumo de agua), que el usuario actualizaría mensualmente.</li>
                            <li><strong>Fecha de Vencimiento/Pago:</strong> La fecha límite o preferida para realizar el pago, crucial para evitar recargos. Se podría incorporar la opción de configurar alertas personalizables días antes del vencimiento.</li>
                            <li><strong>Estado:</strong> Un indicador visual del estado actual del pago.
                                <ul className="list-['-_'] list-inside ml-6 mt-2 space-y-1 text-foreground/80">
                                    <li><span className="text-yellow-500 font-bold">Pendiente:</span> El pago aún no se ha realizado y la fecha de vencimiento está próxima o ya ha pasado.</li>
                                    <li><span className="text-green-500 font-bold">Pagado:</span> El pago ha sido completado y registrado.</li>
                                    <li><span className="text-destructive font-bold">Atrasado:</span> El pago ha vencido y no ha sido registrado como pagado (activaría una notificación).</li>
                                </ul>
                            </li>
                        </ul>
                    </section>

                    <section className="mt-8">
                        <h3 className="text-2xl font-bold mb-4 flex items-center"><History className="mr-3 text-primary h-7 w-7 animate-bounce" />Historial Opcional de Pagos</h3>
                        <p className="text-foreground/90 mb-3">
                            Esta funcionalidad amplía significativamente la utilidad del módulo. Permite al usuario consultar un registro de todos los pagos anteriores de un servicio específico. Esto es invaluable para:
                        </p>
                        <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                            <li><strong>Análisis de Gastos:</strong> Identificar tendencias en el consumo y los costos a lo largo del tiempo.</li>
                            <li><strong>Resolución de Discrepancias:</strong> Tener un registro de pagos para verificar facturas o resolver disputas con proveedores.</li>
                            <li><strong>Control Presupuestario:</strong> Comparar gastos actuales con períodos anteriores.</li>
                            <li><strong>Visualización de Gráficos:</strong> Posibilidad de generar gráficos simples (barras o líneas) para visualizar el comportamiento de un gasto a lo largo del año.</li>
                        </ul>
                    </section>

                    <section className="mt-8">
                        <h3 className="text-2xl font-bold mb-4 flex items-center text-green-500"><ThumbsUp className="mr-3 text-green-500 h-7 w-7 animate-bounce" />Beneficios para el Usuario</h3>
                        <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                            <li><strong>Organización y Control:</strong> Centraliza toda la información de pagos recurrentes en un solo lugar.</li>
                            <li><strong>Reducción de Estrés:</strong> Elimina la preocupación de olvidar un pago importante.</li>
                            <li><strong>Ahorro de Dinero:</strong> Evita multas y recargos por pagos atrasados.</li>
                            <li><strong>Mejor Planificación Financiera:</strong> Permite una visión clara de los gastos fijos mensuales, facilitando la elaboración de presupuestos y la toma de decisiones financieras.</li>
                            <li><strong>Conocimiento del Gasto:</strong> Ofrece una perspectiva clara de en qué se gasta el dinero de forma recurrente.</li>
                        </ul>
                    </section>
                    
                    <section className="mt-8">
                        <h3 className="text-2xl font-bold mb-4 flex items-center text-accent">
                            <Lightbulb className="mr-3 text-accent h-7 w-7" />Consideraciones Futuras ( <span className="animate-scale-pulse inline-block">Próximamente</span> )
                        </h3>
                        <ul className="list-disc list-inside space-y-2 text-foreground/90 pl-4">
                            <li><strong>Notificaciones y Recordatorios:</strong> Alertas configurables vía email o notificaciones push para recordar las fechas de vencimiento.</li>
                            <li><strong>Integración con Calendario:</strong> Sincronización con calendarios personales (Google Calendar, Outlook) para visualizar los pagos como eventos.</li>
                            <li><strong>Reportes y Estadísticas:</strong> Generación de informes sencillos sobre los gastos recurrentes por categoría o período.</li>
                            <li><strong>Gestión de Métodos de Pago:</strong> Posibilidad de asociar cada servicio a un método de pago específico (tarjeta de crédito, cuenta bancaria) para una mejor organización (sin almacenar datos sensibles, solo una referencia).</li>
                            <li><strong>Recibos y Comprobantes:</strong> Opción para adjuntar o enlazar digitalmente recibos o comprobantes de pago.</li>
                        </ul>
                    </section>

                </CardContent>
            </Card>
        </div>
    );
}
