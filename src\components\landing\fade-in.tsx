"use client";

import { useEffect, useRef, useState, type ReactNode, CSSProperties } from 'react';
import { cn } from '@/lib/utils';

interface FadeInProps {
  children: ReactNode;
  className?: string;
  style?: CSSProperties;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export function FadeIn({ children, className, style, direction = 'up' }: FadeInProps) {
  const [isVisible, setIsVisible] = useState(false);
  const domRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ref = domRef.current;
    if (!ref) return;

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(ref);
        }
      });
    }, { threshold: 0.1 });

    observer.observe(ref);
    return () => observer.disconnect();
  }, []);

  const getTransform = () => {
    if (isVisible) return 'translate(0, 0)';
    switch (direction) {
      case 'up': return 'translateY(32px)';
      case 'down': return 'translateY(-32px)';
      case 'left': return 'translateX(32px)';
      case 'right': return 'translateX(-32px)';
      default: return 'translateY(32px)';
    }
  }

  return (
    <div
      ref={domRef}
      className={cn(
        'transition-all duration-1000 ease-out',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={{
        ...style,
        transform: getTransform(),
      }}
    >
      {children}
    </div>
  );
}
