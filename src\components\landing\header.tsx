
"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON>u, X, BotMessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

const navLinks = [
  { name: "Características", href: "#features" },
  { name: "Seguridad", href: "#security" },
];

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    const href = e.currentTarget.href;
    const targetId = href.replace(/.*#/, "");
    const elem = document.getElementById(targetId);
    
    const blockPosition = targetId === 'hero-section' ? 'start' : 'center';

    elem?.scrollIntoView({
      behavior: "smooth",
      block: blockPosition,
    });
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur-sm">
      <div className="container flex h-20 items-center justify-between">
        <Link href="/" className="flex items-center space-x-4 ml-8 transition-transform duration-300 ease-in-out hover:scale-105">
          <BotMessageSquare className="h-12 w-12 text-primary" />
          <span className="text-3xl font-bold">Asistente 360</span>
        </Link>
        <div className="hidden md:flex items-center justify-end space-x-6">
          <nav className="flex items-center space-x-6 text-sm font-medium">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                onClick={handleScroll}
                className="transition-colors hover:text-primary"
              >
                {link.name}
              </Link>
            ))}
          </nav>
          <Button asChild className="font-bold hover:shadow-lg hover:shadow-primary/40 hover:-translate-y-0.5 transition-all duration-300">
            <Link href="#early-access" onClick={handleScroll}>COMENZAR GRATIS</Link>
          </Button>
        </div>
        <div className="md:hidden">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Menu />
                    <span className="sr-only">Abrir menú</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left">
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between pb-4 border-b">
                       <Link href="/" className="mr-6 flex items-center space-x-4 transition-transform duration-300 ease-in-out hover:scale-105" onClick={() => setIsMobileMenuOpen(false)}>
                        <BotMessageSquare className="h-12 w-12 text-primary" />
                        <span className="text-3xl font-bold">Asistente 360</span>
                      </Link>
                      <SheetTrigger asChild>
                         <Button variant="ghost" size="icon">
                            <X />
                            <span className="sr-only">Cerrar menú</span>
                          </Button>
                      </SheetTrigger>
                    </div>
                    <nav className="flex flex-col space-y-4 mt-6">
                      {navLinks.map((link) => (
                        <Link
                          key={link.href}
                          href={link.href}
                          onClick={handleScroll}
                          className="text-lg font-medium transition-colors hover:text-primary"
                        >
                          {link.name}
                        </Link>
                      ))}
                    </nav>
                    <div className="mt-auto">
                        <Button asChild className="w-full font-bold">
                            <Link href="#early-access" onClick={handleScroll}>COMENZAR GRATIS</Link>
                        </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
          </div>
      </div>
    </header>
  );
}
